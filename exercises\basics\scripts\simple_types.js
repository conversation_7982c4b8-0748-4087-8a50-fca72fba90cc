/**
 * TypeScript Simple Types - Comprehensive Guide
 *
 * This file demonstrates TypeScript's basic primitive types with detailed examples
 * and explanations. TypeScript provides static type checking at compile time,
 * helping catch errors before runtime.
 */
console.log("=== TypeScript Simple Types Examples ===\n");
// ========================================
// 1. STRING TYPE
// ========================================
console.log("1. STRING TYPE:");
// Explicit type annotation
var firstName = "Amine";
var lastName = "Mahmoud";
// Type inference - TypeScript can infer the type
var fullName = firstName + " " + lastName; // TypeScript infers this as string
console.log("First Name:", firstName, "- Type:", typeof firstName);
console.log("Last Name:", lastName, "- Type:", typeof lastName);
console.log("Full Name:", fullName, "- Type:", typeof fullName);
// String methods and template literals
var greeting = "Hello, ".concat(fullName, "! Welcome to TypeScript.");
console.log("Greeting:", greeting);
console.log("Greeting length:", greeting.length);
console.log("Uppercase:", greeting.toUpperCase());
// Multi-line strings with template literals
var multiLineString = "\n    This is a multi-line string\n    that spans across multiple lines\n    and preserves formatting.\n";
console.log("Multi-line string:", multiLineString);
// String concatenation vs template literals
var age = 25;
var oldWay = "I am " + age + " years old";
var newWay = "I am ".concat(age, " years old");
console.log("Old way:", oldWay);
console.log("New way:", newWay);
// Type safety - this would cause a compile error:
// firstName = 10; // Error: Type 'number' is not assignable to type 'string'
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 2. NUMBER TYPE
// ========================================
console.log("2. NUMBER TYPE:");
// TypeScript has only one number type (no separate int, float, etc.)
var integerNumber = 42;
var floatingNumber = 3.14159;
var negativeNumber = -100;
var scientificNotation = 1.23e-4;
console.log("Integer:", integerNumber, "- Type:", typeof integerNumber);
console.log("Float:", floatingNumber, "- Type:", typeof floatingNumber);
console.log("Negative:", negativeNumber, "- Type:", typeof negativeNumber);
console.log("Scientific:", scientificNotation, "- Type:", typeof scientificNotation);
// Special number values
var infinityValue = Infinity;
var negativeInfinity = -Infinity;
var notANumber = NaN;
console.log("Infinity:", infinityValue, "- Type:", typeof infinityValue);
console.log("Negative Infinity:", negativeInfinity, "- Type:", typeof negativeInfinity);
console.log("NaN:", notANumber, "- Type:", typeof notANumber);
// Number operations
var sum = integerNumber + floatingNumber;
var product = integerNumber * 2;
var division = integerNumber / 2;
console.log("Sum:", sum);
console.log("Product:", product);
console.log("Division:", division);
// Number methods
console.log("Fixed decimal places:", floatingNumber.toFixed(2));
console.log("Exponential notation:", integerNumber.toExponential(2));
console.log("Precision:", floatingNumber.toPrecision(3));
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 3. BOOLEAN TYPE
// ========================================
console.log("3. BOOLEAN TYPE:");
var isStudent = true;
var isGraduated = false;
var hasJob = !isStudent; // Logical NOT operation
console.log("Is Student:", isStudent, "- Type:", typeof isStudent);
console.log("Is Graduated:", isGraduated, "- Type:", typeof isGraduated);
console.log("Has Job:", hasJob, "- Type:", typeof hasJob);
// Boolean operations
var canApplyForJob = isGraduated && !isStudent;
var needsEducation = !isGraduated || isStudent;
console.log("Can apply for job:", canApplyForJob);
console.log("Needs education:", needsEducation);
// Truthy and falsy values (JavaScript behavior)
var truthyString = Boolean("hello"); // true
var falsyString = Boolean(""); // false
var truthyNumber = Boolean(42); // true
var falsyNumber = Boolean(0); // false
console.log("Boolean('hello'):", truthyString);
console.log("Boolean(''):", falsyString);
console.log("Boolean(42):", truthyNumber);
console.log("Boolean(0):", falsyNumber);
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 4. UNDEFINED AND NULL
// ========================================
console.log("4. UNDEFINED AND NULL:");
var undefinedValue = undefined;
var nullValue = null;
console.log("Undefined value:", undefinedValue, "- Type:", typeof undefinedValue);
console.log("Null value:", nullValue, "- Type:", typeof nullValue);
// Variables without initialization are undefined
var uninitializedVariable;
console.log("Uninitialized variable:", uninitializedVariable, "- Type:", typeof uninitializedVariable);
// Difference between undefined and null
console.log("undefined == null:", undefined == null); // true (loose equality)
console.log("undefined === null:", undefined === null); // false (strict equality)
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 5. JSON PARSING EXAMPLES
// ========================================
console.log("5. JSON PARSING EXAMPLES:");
// JSON.parse converts JSON strings to JavaScript values
var json_1 = JSON.parse("55");
console.log("JSON.parse('55'):", json_1, "- Type:", typeof json_1);
var json_2 = JSON.parse("true");
console.log("JSON.parse('true'):", json_2, "- Type:", typeof json_2);
var json_3 = JSON.parse("null");
console.log("JSON.parse('null'):", json_3, "- Type:", typeof json_3);
// Parsing JSON strings (note the escaped quotes)
var json_4 = JSON.parse("\"hello\"");
console.log("JSON.parse('\"hello\"'):", json_4, "- Type:", typeof json_4);
// Parsing JSON arrays and objects
var json_array = JSON.parse("[1, 2, 3, true, \"test\"]");
console.log("JSON array:", json_array, "- Type:", typeof json_array, "- Is Array:", Array.isArray(json_array));
var json_object = JSON.parse("{\"name\": \"John\", \"age\": 30, \"isActive\": true}");
console.log("JSON object:", json_object, "- Type:", typeof json_object);
// Error handling for invalid JSON
try {
    JSON.parse("hello"); // This will throw an error
    console.log("This won't be reached");
}
catch (error) {
    console.log("JSON parsing error:", error.message);
}
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 6. TYPE INFERENCE AND ANNOTATIONS
// ========================================
console.log("6. TYPE INFERENCE AND ANNOTATIONS:");
// TypeScript can infer types automatically
var inferredString = "TypeScript is smart!"; // inferred as string
var inferredNumber = 42; // inferred as number
var inferredBoolean = true; // inferred as boolean
console.log("Inferred string:", inferredString);
console.log("Inferred number:", inferredNumber);
console.log("Inferred boolean:", inferredBoolean);
// Explicit type annotations (recommended for function parameters and return types)
function greetUser(name, age) {
    return "Hello ".concat(name, ", you are ").concat(age, " years old!");
}
var userGreeting = greetUser("Alice", 28);
console.log("User greeting:", userGreeting);
// Type annotations for variables when type can't be inferred
var userInput; // Will be assigned later
userInput = "Some user input";
console.log("User input:", userInput);
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 7. TYPE CONVERSION AND COERCION
// ========================================
console.log("7. TYPE CONVERSION AND COERCION:");
// Explicit type conversion
var stringNumber = "123";
var convertedToNumber = Number(stringNumber);
var convertedToInt = parseInt(stringNumber);
var convertedToFloat = parseFloat("123.45");
console.log("String '123' to number:", convertedToNumber, "- Type:", typeof convertedToNumber);
console.log("String '123' to int:", convertedToInt, "- Type:", typeof convertedToInt);
console.log("String '123.45' to float:", convertedToFloat, "- Type:", typeof convertedToFloat);
// Converting numbers to strings
var numberToString = String(42);
var numberToStringMethod = (42).toString();
var numberToStringTemplate = "".concat(42);
console.log("Number 42 to string (String()):", numberToString, "- Type:", typeof numberToString);
console.log("Number 42 to string (.toString()):", numberToStringMethod, "- Type:", typeof numberToStringMethod);
console.log("Number 42 to string (template):", numberToStringTemplate, "- Type:", typeof numberToStringTemplate);
// Boolean conversion
var stringToBoolean = Boolean("hello"); // true
var emptyStringToBoolean = Boolean(""); // false
var numberToBoolean = Boolean(1); // true
var zeroToBoolean = Boolean(0); // false
console.log("'hello' to boolean:", stringToBoolean);
console.log("'' to boolean:", emptyStringToBoolean);
console.log("1 to boolean:", numberToBoolean);
console.log("0 to boolean:", zeroToBoolean);
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 8. COMMON TYPE CHECKING PATTERNS
// ========================================
console.log("8. COMMON TYPE CHECKING PATTERNS:");
// Using typeof for runtime type checking
function processValue(value) {
    if (typeof value === "string") {
        return "String value: ".concat(value.toUpperCase());
    }
    else if (typeof value === "number") {
        return "Number value: ".concat(value.toFixed(2));
    }
    else if (typeof value === "boolean") {
        return "Boolean value: ".concat(value ? "TRUE" : "FALSE");
    }
    else {
        return "Unknown type: ".concat(typeof value);
    }
}
console.log(processValue("hello"));
console.log(processValue(42.567));
console.log(processValue(true));
console.log(processValue(null));
console.log(processValue(undefined));
// Type guards and narrowing
function isString(value) {
    return typeof value === "string";
}
function isNumber(value) {
    return typeof value === "number" && !isNaN(value);
}
var mixedValue = "TypeScript";
if (isString(mixedValue)) {
    // TypeScript knows mixedValue is a string here
    console.log("String length:", mixedValue.length);
}
mixedValue = 42;
if (isNumber(mixedValue)) {
    // TypeScript knows mixedValue is a number here
    console.log("Number squared:", mixedValue * mixedValue);
}
console.log("\n" + "=".repeat(50) + "\n");
// ========================================
// 9. BEST PRACTICES AND TIPS
// ========================================
console.log("9. BEST PRACTICES AND TIPS:");
console.log("✅ Best Practices:");
console.log("1. Use type inference when the type is obvious");
console.log("2. Add explicit type annotations for function parameters and return types");
console.log("3. Use strict null checks (enabled by default in strict mode)");
console.log("4. Prefer const over let when the value won't change");
console.log("5. Use meaningful variable names that indicate their purpose");
// Examples of good practices
var PI = 3.14159; // const for constants
var APP_NAME = "TypeScript Learning App"; // descriptive names
function calculateCircleArea(radius) {
    return PI * radius * radius;
}
var circleArea = calculateCircleArea(5);
console.log("".concat(APP_NAME, ": Circle area with radius 5: ").concat(circleArea.toFixed(2)));
console.log("\n❌ Common Mistakes to Avoid:");
console.log("1. Don't use 'any' type unless absolutely necessary");
console.log("2. Don't ignore TypeScript compiler errors");
console.log("3. Don't mix up == and === (prefer strict equality)");
console.log("4. Don't forget to handle null/undefined cases");
console.log("\n" + "=".repeat(50));
console.log("🎉 TypeScript Simple Types Tutorial Complete!");
console.log("=".repeat(50));
