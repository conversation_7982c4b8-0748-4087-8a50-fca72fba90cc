# basics

- this is typescript exercises for basic level using [[w3schools](https://www.w3schools.com/typescript/index.php)]

1. Installing the Compiler

```pwsh
npm install typescript --save-dev
npx tsc
```

2. Configuring the compiler

- this will create tsconfig.json file:

```pwsh
npx tsc --init
```

3. TypeScript Simple Types:

- [exercises\basics\scripts\simple_types.ts](/exercises/basics/scripts/simple_types.ts)