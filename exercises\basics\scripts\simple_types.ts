let firstName: string = "Amine";

console.log("the type of the variable firstname is: ", typeof(firstName));
console.log(`The value of firstName is: ${firstName}`);

// below example will not compile because firstName is a string 
// and we are trying to assign a number to it

/*
* try {
*     firstName = 10;
* } catch (error) {
*     console.log(error);
* }
*/

const json_1 = JSON.parse("55");
// this will print number because parse did convert the string to an object and in our case it is a number
console.log(typeof json_1);

const json_2 = JSON.parse("true");
// this will print boolean because parse did convert the string to an object and in our case it is a boolean
console.log(typeof json_2);

//this will not work In JSON, "hello" would need to be "\"hello\"" (i.e., a string containing a JSON string).

// this will raise an error because hello is not a valid json
/* const json_3 = JSON.parse("hello");
console.log(typeof json_3);
*/
const json_4 = JSON.parse("\"hello\"");
console.log(typeof json_4);