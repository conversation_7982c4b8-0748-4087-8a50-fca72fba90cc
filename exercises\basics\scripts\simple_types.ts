/**
 * TypeScript Simple Types - Comprehensive Guide
 *
 * This file demonstrates TypeScript's basic primitive types with detailed examples
 * and explanations. TypeScript provides static type checking at compile time,
 * helping catch errors before runtime.
 */

console.log("=== TypeScript Simple Types Examples ===\n");

// ========================================
// 1. STRING TYPE
// ========================================
console.log("1. STRING TYPE:");

// Explicit type annotation
let firstName: string = "Amine";
let lastName: string = "Mahmoud";

// Type inference - TypeScript can infer the type
let fullName = firstName + " " + lastName; // TypeScript infers this as string

console.log("First Name:", firstName, "- Type:", typeof firstName);
console.log("Last Name:", lastName, "- Type:", typeof lastName);
console.log("Full Name:", fullName, "- Type:", typeof fullName);

// String methods and template literals
let greeting: string = `Hello, ${fullName}! Welcome to TypeScript.`;
console.log("Greeting:", greeting);
console.log("Greeting length:", greeting.length);
console.log("Uppercase:", greeting.toUpperCase());

// Multi-line strings with template literals
let multiLineString: string = `
    This is a multi-line string
    that spans across multiple lines
    and preserves formatting.
`;
console.log("Multi-line string:", multiLineString);

// String concatenation vs template literals
let age: number = 25;
let oldWay: string = "I am " + age + " years old";
let newWay: string = `I am ${age} years old`;
console.log("Old way:", oldWay);
console.log("New way:", newWay);

// Type safety - this would cause a compile error:
// firstName = 10; // Error: Type 'number' is not assignable to type 'string'

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 2. NUMBER TYPE
// ========================================
console.log("2. NUMBER TYPE:");

// TypeScript has only one number type (no separate int, float, etc.)
let integerNumber: number = 42;
let floatingNumber: number = 3.14159;
let negativeNumber: number = -100;
let scientificNotation: number = 1.23e-4;

console.log("Integer:", integerNumber, "- Type:", typeof integerNumber);
console.log("Float:", floatingNumber, "- Type:", typeof floatingNumber);
console.log("Negative:", negativeNumber, "- Type:", typeof negativeNumber);
console.log("Scientific:", scientificNotation, "- Type:", typeof scientificNotation);

// Special number values
let infinityValue: number = Infinity;
let negativeInfinity: number = -Infinity;
let notANumber: number = NaN;

console.log("Infinity:", infinityValue, "- Type:", typeof infinityValue);
console.log("Negative Infinity:", negativeInfinity, "- Type:", typeof negativeInfinity);
console.log("NaN:", notANumber, "- Type:", typeof notANumber);

// Number operations
let sum: number = integerNumber + floatingNumber;
let product: number = integerNumber * 2;
let division: number = integerNumber / 2;

console.log("Sum:", sum);
console.log("Product:", product);
console.log("Division:", division);

// Number methods
console.log("Fixed decimal places:", floatingNumber.toFixed(2));
console.log("Exponential notation:", integerNumber.toExponential(2));
console.log("Precision:", floatingNumber.toPrecision(3));

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 3. BOOLEAN TYPE
// ========================================
console.log("3. BOOLEAN TYPE:");

let isStudent: boolean = true;
let isGraduated: boolean = false;
let hasJob: boolean = !isStudent; // Logical NOT operation

console.log("Is Student:", isStudent, "- Type:", typeof isStudent);
console.log("Is Graduated:", isGraduated, "- Type:", typeof isGraduated);
console.log("Has Job:", hasJob, "- Type:", typeof hasJob);

// Boolean operations
let canApplyForJob: boolean = isGraduated && !isStudent;
let needsEducation: boolean = !isGraduated || isStudent;

console.log("Can apply for job:", canApplyForJob);
console.log("Needs education:", needsEducation);

// Truthy and falsy values (JavaScript behavior)
let truthyString: boolean = Boolean("hello"); // true
let falsyString: boolean = Boolean(""); // false
let truthyNumber: boolean = Boolean(42); // true
let falsyNumber: boolean = Boolean(0); // false

console.log("Boolean('hello'):", truthyString);
console.log("Boolean(''):", falsyString);
console.log("Boolean(42):", truthyNumber);
console.log("Boolean(0):", falsyNumber);

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 4. UNDEFINED AND NULL
// ========================================
console.log("4. UNDEFINED AND NULL:");

let undefinedValue: undefined = undefined;
let nullValue: null = null;

console.log("Undefined value:", undefinedValue, "- Type:", typeof undefinedValue);
console.log("Null value:", nullValue, "- Type:", typeof nullValue);

// Variables without initialization are undefined
let uninitializedVariable: string | undefined;
console.log("Uninitialized variable:", uninitializedVariable, "- Type:", typeof uninitializedVariable);

// Difference between undefined and null
console.log("undefined == null:", undefined == null); // true (loose equality)
console.log("undefined === null:", undefined === null); // false (strict equality)

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 5. JSON PARSING EXAMPLES
// ========================================
console.log("5. JSON PARSING EXAMPLES:");

// JSON.parse converts JSON strings to JavaScript values
const json_1 = JSON.parse("55");
console.log("JSON.parse('55'):", json_1, "- Type:", typeof json_1);

const json_2 = JSON.parse("true");
console.log("JSON.parse('true'):", json_2, "- Type:", typeof json_2);

const json_3 = JSON.parse("null");
console.log("JSON.parse('null'):", json_3, "- Type:", typeof json_3);

// Parsing JSON strings (note the escaped quotes)
const json_4 = JSON.parse("\"hello\"");
console.log("JSON.parse('\"hello\"'):", json_4, "- Type:", typeof json_4);

// Parsing JSON arrays and objects
const json_array = JSON.parse("[1, 2, 3, true, \"test\"]");
console.log("JSON array:", json_array, "- Type:", typeof json_array, "- Is Array:", Array.isArray(json_array));

const json_object = JSON.parse("{\"name\": \"John\", \"age\": 30, \"isActive\": true}");
console.log("JSON object:", json_object, "- Type:", typeof json_object);

// Error handling for invalid JSON
try {
    JSON.parse("hello"); // This will throw an error
    console.log("This won't be reached");
} catch (error) {
    console.log("JSON parsing error:", (error as Error).message);
}

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 6. TYPE INFERENCE AND ANNOTATIONS
// ========================================
console.log("6. TYPE INFERENCE AND ANNOTATIONS:");

// TypeScript can infer types automatically
let inferredString = "TypeScript is smart!"; // inferred as string
let inferredNumber = 42; // inferred as number
let inferredBoolean = true; // inferred as boolean

console.log("Inferred string:", inferredString);
console.log("Inferred number:", inferredNumber);
console.log("Inferred boolean:", inferredBoolean);

// Explicit type annotations (recommended for function parameters and return types)
function greetUser(name: string, age: number): string {
    return `Hello ${name}, you are ${age} years old!`;
}

let userGreeting: string = greetUser("Alice", 28);
console.log("User greeting:", userGreeting);

// Type annotations for variables when type can't be inferred
let userInput: string; // Will be assigned later
userInput = "Some user input";
console.log("User input:", userInput);

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 7. TYPE CONVERSION AND COERCION
// ========================================
console.log("7. TYPE CONVERSION AND COERCION:");

// Explicit type conversion
let stringNumber: string = "123";
let convertedToNumber: number = Number(stringNumber);
let convertedToInt: number = parseInt(stringNumber);
let convertedToFloat: number = parseFloat("123.45");

console.log("String '123' to number:", convertedToNumber, "- Type:", typeof convertedToNumber);
console.log("String '123' to int:", convertedToInt, "- Type:", typeof convertedToInt);
console.log("String '123.45' to float:", convertedToFloat, "- Type:", typeof convertedToFloat);

// Converting numbers to strings
let numberToString: string = String(42);
let numberToStringMethod: string = (42).toString();
let numberToStringTemplate: string = `${42}`;

console.log("Number 42 to string (String()):", numberToString, "- Type:", typeof numberToString);
console.log("Number 42 to string (.toString()):", numberToStringMethod, "- Type:", typeof numberToStringMethod);
console.log("Number 42 to string (template):", numberToStringTemplate, "- Type:", typeof numberToStringTemplate);

// Boolean conversion
let stringToBoolean: boolean = Boolean("hello"); // true
let emptyStringToBoolean: boolean = Boolean(""); // false
let numberToBoolean: boolean = Boolean(1); // true
let zeroToBoolean: boolean = Boolean(0); // false

console.log("'hello' to boolean:", stringToBoolean);
console.log("'' to boolean:", emptyStringToBoolean);
console.log("1 to boolean:", numberToBoolean);
console.log("0 to boolean:", zeroToBoolean);

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 8. COMMON TYPE CHECKING PATTERNS
// ========================================
console.log("8. COMMON TYPE CHECKING PATTERNS:");

// Using typeof for runtime type checking
function processValue(value: unknown): string {
    if (typeof value === "string") {
        return `String value: ${value.toUpperCase()}`;
    } else if (typeof value === "number") {
        return `Number value: ${value.toFixed(2)}`;
    } else if (typeof value === "boolean") {
        return `Boolean value: ${value ? "TRUE" : "FALSE"}`;
    } else {
        return `Unknown type: ${typeof value}`;
    }
}

console.log(processValue("hello"));
console.log(processValue(42.567));
console.log(processValue(true));
console.log(processValue(null));
console.log(processValue(undefined));

// Type guards and narrowing
function isString(value: unknown): value is string {
    return typeof value === "string";
}

function isNumber(value: unknown): value is number {
    return typeof value === "number" && !isNaN(value);
}

let mixedValue: unknown = "TypeScript";
if (isString(mixedValue)) {
    // TypeScript knows mixedValue is a string here
    console.log("String length:", mixedValue.length);
}

mixedValue = 42;
if (isNumber(mixedValue)) {
    // TypeScript knows mixedValue is a number here
    console.log("Number squared:", mixedValue * mixedValue);
}

console.log("\n" + "=".repeat(50) + "\n");

// ========================================
// 9. BEST PRACTICES AND TIPS
// ========================================
console.log("9. BEST PRACTICES AND TIPS:");

console.log("✅ Best Practices:");
console.log("1. Use type inference when the type is obvious");
console.log("2. Add explicit type annotations for function parameters and return types");
console.log("3. Use strict null checks (enabled by default in strict mode)");
console.log("4. Prefer const over let when the value won't change");
console.log("5. Use meaningful variable names that indicate their purpose");

// Examples of good practices
const PI: number = 3.14159; // const for constants
const APP_NAME: string = "TypeScript Learning App"; // descriptive names

function calculateCircleArea(radius: number): number { // explicit types for functions
    return PI * radius * radius;
}

let circleArea: number = calculateCircleArea(5);
console.log(`${APP_NAME}: Circle area with radius 5: ${circleArea.toFixed(2)}`);

console.log("\n❌ Common Mistakes to Avoid:");
console.log("1. Don't use 'any' type unless absolutely necessary");
console.log("2. Don't ignore TypeScript compiler errors");
console.log("3. Don't mix up == and === (prefer strict equality)");
console.log("4. Don't forget to handle null/undefined cases");

console.log("\n" + "=".repeat(50));
console.log("🎉 TypeScript Simple Types Tutorial Complete!");
console.log("=".repeat(50));